#!/opt/homebrew/bin/bash

# ==============================================================================
# Firebase Push Notification Script
# ==============================================================================
# This script sends push notifications to your Android emulator app via Firebase
#
# Usage:
#   - ./send_prod.sh "***_fcm_token_***"
#
# ==============================================================================

# Configuration Constants
readonly FIREBASE_PROJECT_ID="appio-production"
readonly SERVICE_ACCOUNT_FILE=".keys/firebase-prod.json"
RANDOM_ID=$((RANDOM % 90000 + 10000))  # generates 10000–99999

# Declare associative array for custom data
declare -A CUSTOM_DATA
CUSTOM_DATA["notification_id"]="ntf_000000000000000000000$RANDOM_ID"
CUSTOM_DATA["service_id"]="svc_00000000000000000000000000"
#CUSTOM_DATA["service_title"]="Production Service"
CUSTOM_DATA["logo_url"]="https://cdn.appio.so/app/demo.appio.so/logo.png"
#CUSTOM_DATA["image_url"]="https://cdn.appio.so/app/demo.appio.so/banner.jpg"
#CUSTOM_DATA["category"]="msg"
#CUSTOM_DATA["action_category"]="APPIO_YES_NO"

# ==============================================================================

# Source the core functionality
source "$(dirname "$0")/send_core.sh"

# Run the main function with all arguments
main "$@"